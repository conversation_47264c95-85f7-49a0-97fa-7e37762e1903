<?php $__env->startSection('title', 'Manajemen Retur - Dashboard Supplier'); ?>
<?php $__env->startSection('page-title', 'Manajemen Retur'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Retur dari Gudang</h1>
                    <p class="text-gray-600 mt-1">Tinjau dan kelola retur produk dari gudang pusat</p>
                </div>
                <div class="supplier-dashboard-info-card">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="text-sm">
                            <div class="font-medium text-gray-900">Aksi yang Tersedia:</div>
                            <div class="text-gray-600">Setujui pengiriman yang dibatalkan untuk menyelesaikan proses</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon blue">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value"><?php echo e(number_format($stats['total_returns'])); ?></div>
            <div class="supplier-dashboard-stat-label">Total Retur</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon yellow">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value"><?php echo e(number_format($stats['requested_returns'])); ?></div>
            <div class="supplier-dashboard-stat-label">Retur Diminta</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon green">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value"><?php echo e(number_format($stats['approved_returns'])); ?></div>
            <div class="supplier-dashboard-stat-label">Retur Disetujui</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon purple">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value"><?php echo e(number_format($stats['completed_returns'])); ?></div>
            <div class="supplier-dashboard-stat-label">Retur Selesai</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon red">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value"><?php echo e(number_format($stats['cancelled_deliveries'])); ?></div>
            <div class="supplier-dashboard-stat-label">Pengiriman Dibatalkan</div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Month Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Periode</label>
                    <input type="month"
                           name="month"
                           value="<?php echo e($filterMonth); ?>"
                           class="supplier-dashboard-input">
                </div>

                <!-- Search -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Cari</label>
                    <input type="text"
                           name="search"
                           value="<?php echo e(request('search')); ?>"
                           placeholder="Cari produk..."
                           class="supplier-dashboard-input">
                </div>

                <!-- Filter Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full supplier-dashboard-btn supplier-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Returns Table -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h2 class="supplier-dashboard-card-title">Retur dari Gudang</h2>
        </div>
        <div class="supplier-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <!-- <th class="px-6 py-3">Produk</th> -->
                            <th class="px-6 py-3">Sumber</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Alasan</th>
                            <th class="px-6 py-3">Tanggal</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $returns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $return): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <!-- <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($return->product->name ?? 'N/A'); ?></div>
                                <div class="text-xs text-gray-500 mt-1"><?php echo e($return->product->category ?? 'Tanpa Kategori'); ?></div>
                            </td> -->
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($return->source); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e(number_format($return->quantity)); ?></div>
                                <div class="text-xs text-gray-500">unit</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-600"><?php echo e($return->reason_in_indonesian); ?></div>
                                <?php if($return->description): ?>
                                <div class="text-xs text-gray-500 mt-1"><?php echo e(Str::limit($return->description, 50)); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($return->return_date ? $return->return_date->format('d M Y') : 'N/A'); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="supplier-dashboard-returns-actions">
                                    <?php if($return->status === 'requested'): ?>
                                        <!-- Simple Approve Button -->
                                        <button onclick="openApproveModal('<?php echo e($return->id); ?>', '<?php echo e($return->product->name ?? 'N/A'); ?>')"
                                                class="supplier-dashboard-btn supplier-dashboard-btn-success">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Setujui
                                        </button>
                                    <?php else: ?>
                                        <span class="text-xs text-gray-500"><?php echo e($return->status_in_indonesian); ?></span>
                                    <?php endif; ?>

                                    <!-- View Details Link -->
                                    <a href="<?php echo e(route('supplier.returns.show', $return)); ?>"
                                       class="text-xs text-blue-600 hover:text-blue-800 text-center font-medium">
                                        Lihat Detail
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum ada permintaan retur</p>
                                    <p>Permintaan retur dari toko akan muncul di sini</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Cancelled Deliveries Table -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h2 class="supplier-dashboard-card-title">Pengiriman yang Dibatalkan</h2>
        </div>
        <div class="supplier-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Harga Satuan</th>
                            <th class="px-6 py-3">Total Harga</th>
                            <th class="px-6 py-3">Tanggal Pengiriman</th>
                            <th class="px-6 py-3">Catatan</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $cancelledDeliveries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $delivery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($delivery->product->name ?? 'N/A'); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e(number_format($delivery->quantity)); ?> unit</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">Rp <?php echo e(number_format($delivery->unit_price, 0, ',', '.')); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">Rp <?php echo e(number_format($delivery->total_price, 0, ',', '.')); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($delivery->delivery_date ? $delivery->delivery_date->format('d M Y') : 'N/A'); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-600"><?php echo e($delivery->notes ?? 'Tidak ada catatan'); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="supplier-dashboard-returns-actions">
                                    <button onclick="openApproveCancelledModal('<?php echo e($delivery->id); ?>', '<?php echo e($delivery->product->name ?? 'N/A'); ?>')"
                                            class="supplier-dashboard-btn supplier-dashboard-btn-success">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        Disetujui
                                    </button>
                                    <button onclick="openViewCancelledModal('<?php echo e($delivery->id); ?>', '<?php echo e($delivery->product->name ?? 'N/A'); ?>', '<?php echo e($delivery->quantity); ?>', '<?php echo e($delivery->notes ?? 'Tidak ada catatan'); ?>')"
                                            class="supplier-dashboard-btn supplier-dashboard-btn-outline">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        Lihat
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum ada pengiriman yang dibatalkan</p>
                                    <p>Pengiriman yang dibatalkan akan muncul di sini</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

        </div>
    </div>
</div>

<!-- Approve Return Modal -->
<div id="approveModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Setujui Retur</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeApproveModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="approveForm" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            <div class="supplier-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Konfirmasi Persetujuan</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Anda akan menyetujui retur untuk produk <strong id="approveProductName"></strong>.
                        </p>
                        <p class="text-sm text-green-600 font-medium">
                            ✓ Retur akan disetujui dan dipindahkan ke riwayat.
                        </p>
                    </div>
                </div>

                <div class="mt-4">
                    <label for="approve_notes" class="block text-sm font-medium text-gray-700 mb-2">
                        Catatan (Opsional)
                    </label>
                    <textarea id="approve_notes"
                              name="notes"
                              rows="3"
                              class="supplier-dashboard-textarea"
                              placeholder="Catatan untuk persetujuan retur..."></textarea>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeApproveModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-success">
                    Ya, Setujui Retur
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Approve Cancelled Delivery Modal -->
<div id="approveCancelledModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Setujui Pengiriman yang Dibatalkan</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeApproveCancelledModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="approveCancelledForm" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            <div class="supplier-dashboard-modal-body">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Konfirmasi Persetujuan</h4>
                    <p class="text-gray-600">
                        Apakah Anda yakin ingin menyetujui pengiriman yang dibatalkan untuk produk
                        <span class="font-semibold" id="cancelledProductName"></span>?
                    </p>
                    <p class="text-sm text-gray-500 mt-2">
                        Tindakan ini akan menyelesaikan proses dan memindahkan data ke riwayat.
                    </p>
                </div>
                <div class="space-y-4">
                    <label class="block text-sm font-medium text-gray-700">
                        Catatan (Opsional)
                    </label>
                    <textarea name="notes" rows="3"
                              class="supplier-dashboard-textarea"
                              placeholder="Catatan untuk persetujuan pengiriman yang dibatalkan..."></textarea>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeApproveCancelledModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-success">
                    Ya, Setujui
                </button>
            </div>
        </form>
    </div>
</div>

<!-- View Cancelled Delivery Modal -->
<div id="viewCancelledModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Detail Pengiriman yang Dibatalkan</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeViewCancelledModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="supplier-dashboard-modal-body">
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Nama Produk</label>
                    <div class="text-gray-900 font-semibold" id="viewProductName"></div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Jumlah</label>
                    <div class="text-gray-900" id="viewQuantity"></div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Catatan Pembatalan</label>
                    <div class="text-gray-900" id="viewNotes"></div>
                </div>
            </div>
        </div>
        <div class="supplier-dashboard-modal-footer">
            <button type="button" onclick="closeViewCancelledModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                Tutup
            </button>
        </div>
    </div>
</div>





<?php $__env->startPush('scripts'); ?>
<script>
// Approve Modal Functions
function openApproveModal(returnId, productName) {
    const modal = document.getElementById('approveModal');
    const form = document.getElementById('approveForm');
    const productNameElement = document.getElementById('approveProductName');

    form.action = `/supplier/returns/${returnId}/approve`;
    productNameElement.textContent = productName;

    modal.classList.add('active');
}

function closeApproveModal() {
    const modal = document.getElementById('approveModal');
    modal.classList.remove('active');
    document.getElementById('approveForm').reset();
}

// Cancelled Delivery Functions (for the cancelled deliveries section)
function openApproveCancelledModal(deliveryId, productName) {
    const modal = document.getElementById('approveCancelledModal');
    const form = document.getElementById('approveCancelledForm');
    const productNameSpan = document.getElementById('cancelledProductName');

    productNameSpan.textContent = productName;
    form.action = `/supplier/cancelled-deliveries/${deliveryId}/approve`;
    modal.classList.add('active');
}

function closeApproveCancelledModal() {
    const modal = document.getElementById('approveCancelledModal');
    modal.classList.remove('active');
    document.getElementById('approveCancelledForm').reset();
}

function openViewCancelledModal(deliveryId, productName, quantity, notes) {
    const modal = document.getElementById('viewCancelledModal');
    document.getElementById('viewProductName').textContent = productName;
    document.getElementById('viewQuantity').textContent = quantity + ' unit';
    document.getElementById('viewNotes').textContent = notes;
    modal.classList.add('active');
}

function closeViewCancelledModal() {
    const modal = document.getElementById('viewCancelledModal');
    modal.classList.remove('active');
}

// Close modals when clicking outside
document.getElementById('approveModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeApproveModal();
    }
});

document.getElementById('approveCancelledModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeApproveCancelledModal();
    }
});

document.getElementById('viewCancelledModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeViewCancelledModal();
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.supplier', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/supplier/returns/index.blade.php ENDPATH**/ ?>