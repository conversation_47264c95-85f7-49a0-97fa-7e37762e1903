/* PT. Indah Berkah Abadi - Admin Dashboard Return History */
/* Professional table styling with admin-dashboard-* class prefixes */

/* ===== TABLE STYLING ===== */

.admin-dashboard-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    background: white;
}

.admin-dashboard-table thead {
    background: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
}

.admin-dashboard-table thead th {
    padding: 16px 24px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid #e2e8f0;
}

.admin-dashboard-table tbody tr {
    border-bottom: 1px solid #f1f5f9;
    transition: background-color 0.2s ease;
}

.admin-dashboard-table tbody tr:hover {
    background-color: #f8fafc;
}

.admin-dashboard-table tbody tr:last-child {
    border-bottom: none;
}

.admin-dashboard-table tbody td {
    padding: 16px 24px;
    vertical-align: top;
    color: #374151;
    border-bottom: 1px solid #f1f5f9;
}

.admin-dashboard-table tbody td:first-child {
    font-weight: 500;
}

/* ===== STATUS BADGES ===== */

.admin-dashboard-status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.admin-dashboard-status-badge.admin-dashboard-status-requested {
    background-color: #fef3c7;
    color: #d97706;
    border: 1px solid #fbbf24;
}

.admin-dashboard-status-badge.admin-dashboard-status-approved {
    background-color: #d1fae5;
    color: #059669;
    border: 1px solid #34d399;
}

.admin-dashboard-status-badge.admin-dashboard-status-completed {
    background-color: #dbeafe;
    color: #3b82f6;
    border: 1px solid #60a5fa;
}

.admin-dashboard-status-badge.admin-dashboard-status-rejected {
    background-color: #fee2e2;
    color: #dc2626;
    border: 1px solid #f87171;
}

/* ===== CARD ENHANCEMENTS ===== */

.admin-dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    border: 1px solid #e2e8f0;
}

.admin-dashboard-card-header {
    padding: 24px;
    border-bottom: 1px solid #e2e8f0;
    background: #fafbfc;
}

.admin-dashboard-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.admin-dashboard-card-content {
    padding: 0;
}

/* ===== TABLE CONTAINER ===== */

.admin-dashboard-table-container {
    overflow-x: auto;
    border-radius: 0 0 12px 12px;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
    .admin-dashboard-table {
        font-size: 0.8125rem;
        min-width: 800px;
    }
    
    .admin-dashboard-table thead th,
    .admin-dashboard-table tbody td {
        padding: 12px 16px;
    }
    
    .admin-dashboard-card-header {
        padding: 16px;
    }
    
    .admin-dashboard-status-badge {
        font-size: 0.6875rem;
        padding: 3px 8px;
    }
}

@media (max-width: 480px) {
    .admin-dashboard-table {
        font-size: 0.75rem;
        min-width: 700px;
    }
    
    .admin-dashboard-table thead th,
    .admin-dashboard-table tbody td {
        padding: 8px 12px;
    }
    
    .admin-dashboard-card-header {
        padding: 12px;
    }
    
    .admin-dashboard-card-title {
        font-size: 1.125rem;
    }
}

/* ===== EMPTY STATE STYLING ===== */

.admin-dashboard-empty-state {
    text-align: center;
    padding: 48px 24px;
    color: #6b7280;
}

.admin-dashboard-empty-state-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;
    color: #d1d5db;
}

.admin-dashboard-empty-state-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.admin-dashboard-empty-state-description {
    font-size: 0.875rem;
    color: #6b7280;
    max-width: 400px;
    margin: 0 auto;
}
