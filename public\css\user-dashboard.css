/* PT. Indah Berkah Abadi - User Dashboard CSS */
/* Consistent design system with user-dashboard-* prefixes */
/*
 * Z-INDEX HIERARCHY:
 * - Sidebar navigation: 1000+ (highest priority)
 * - Dropdown elements: 10-50 (medium priority, below sidebar)
 * - Interactive elements: 2-5 (low priority)
 * - Main content: 1 (lowest priority)
 */

/* ===== LAYOUT COMPONENTS ===== */

.user-dashboard-container {
  @apply space-y-4 lg:space-y-6;
}

.user-dashboard-header {
  @apply flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4;
}

.user-dashboard-header-content {
  @apply flex-1;
}

.user-dashboard-header-title {
  @apply text-2xl sm:text-3xl font-bold text-gray-900;
}

.user-dashboard-header-subtitle {
  @apply text-gray-600 mt-1;
}

.user-dashboard-header-actions {
  @apply flex flex-col sm:flex-row gap-3;
}

/* ===== CARD COMPONENTS ===== */

.user-dashboard-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.user-dashboard-card-header {
  @apply px-4 py-5 sm:px-6 border-b border-gray-200;
}

.user-dashboard-card-title {
  @apply text-lg font-semibold text-gray-900;
}

.user-dashboard-card-description {
  @apply text-sm text-gray-600 mt-1;
}

.user-dashboard-card-content {
  @apply px-4 py-5 sm:px-6;
}

/* ===== BUTTON COMPONENTS ===== */

.user-dashboard-btn {
  @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm transition-colors duration-200 min-h-[44px];
}

.user-dashboard-btn-primary {
  @apply user-dashboard-btn bg-green-600 text-white hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2;
}

.user-dashboard-btn-secondary {
  @apply user-dashboard-btn bg-gray-600 text-white hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
}

.user-dashboard-btn-outline {
  @apply user-dashboard-btn bg-white text-gray-700 border-gray-300 hover:bg-gray-50 focus:ring-2 focus:ring-green-500 focus:ring-offset-2;
}

.user-dashboard-btn-sm {
  @apply px-3 py-2 text-xs min-h-[40px];
}

.user-dashboard-btn-lg {
  @apply px-6 py-3 text-base min-h-[48px];
}

/* ===== FORM COMPONENTS ===== */

.user-dashboard-form-group {
  @apply space-y-1;
}

.user-dashboard-form-label {
  @apply block text-sm font-medium text-gray-700;
}

.user-dashboard-form-input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 min-h-[44px];
}

.user-dashboard-form-select {
  @apply user-dashboard-form-input;
}

.user-dashboard-form-error {
  @apply text-sm text-red-600 mt-1;
}

.user-dashboard-form-help {
  @apply text-xs text-gray-500 mt-1;
}

/* ===== TABLE COMPONENTS ===== */

.user-dashboard-table-container {
  @apply overflow-x-auto;
}

.user-dashboard-table {
  @apply min-w-full divide-y divide-gray-200;
}

.user-dashboard-table-header {
  @apply bg-gray-50;
}

.user-dashboard-table-header-cell {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.user-dashboard-table-body {
  @apply bg-white divide-y divide-gray-200;
}

.user-dashboard-table-row {
  @apply hover:bg-gray-50;
}

.user-dashboard-table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

/* ===== BADGE COMPONENTS ===== */

.user-dashboard-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.user-dashboard-badge-green {
  @apply user-dashboard-badge bg-green-100 text-green-800;
}

.user-dashboard-badge-yellow {
  @apply user-dashboard-badge bg-yellow-100 text-yellow-800;
}

.user-dashboard-badge-red {
  @apply user-dashboard-badge bg-red-100 text-red-800;
}

.user-dashboard-badge-blue {
  @apply user-dashboard-badge bg-blue-100 text-blue-800;
}

.user-dashboard-badge-gray {
  @apply user-dashboard-badge bg-gray-100 text-gray-800;
}

.user-dashboard-badge-orange {
  @apply user-dashboard-badge bg-orange-100 text-orange-800;
}

/* ===== STAT CARD COMPONENTS ===== */

.user-dashboard-stat-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4;
}

.user-dashboard-stat-card {
  @apply user-dashboard-card p-4;
}

.user-dashboard-stat-header {
  @apply flex items-center justify-between;
}

.user-dashboard-stat-title {
  @apply text-sm font-medium text-gray-500;
}

.user-dashboard-stat-icon {
  @apply h-5 w-5 text-gray-400;
}

.user-dashboard-stat-value {
  @apply text-2xl font-bold text-gray-900 mt-2;
}

.user-dashboard-stat-description {
  @apply text-xs text-gray-500 mt-1;
}

/* ===== LIST COMPONENTS ===== */

.user-dashboard-list {
  @apply space-y-4;
}

.user-dashboard-list-item {
  @apply user-dashboard-card p-4;
}

.user-dashboard-list-item-content {
  @apply flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4;
}

.user-dashboard-list-item-main {
  @apply flex-1;
}

.user-dashboard-list-item-title {
  @apply font-semibold text-gray-900;
}

.user-dashboard-list-item-subtitle {
  @apply text-sm text-gray-600 mt-1;
}

.user-dashboard-list-item-meta {
  @apply text-sm text-gray-500 mt-1;
}

.user-dashboard-list-item-actions {
  @apply flex flex-col sm:flex-row gap-2;
}

/* ===== SEARCH AND FILTER COMPONENTS ===== */

.user-dashboard-search-container {
  @apply user-dashboard-card;
}

.user-dashboard-search-form {
  @apply grid grid-cols-1 md:grid-cols-4 gap-4;
}

.user-dashboard-filter-group {
  @apply user-dashboard-form-group;
}

/* ===== EMPTY STATE COMPONENTS ===== */

.user-dashboard-empty-state {
  @apply text-center py-12;
}

.user-dashboard-empty-icon {
  @apply mx-auto h-12 w-12 text-gray-400;
}

.user-dashboard-empty-title {
  @apply mt-2 text-sm font-medium text-gray-900;
}

.user-dashboard-empty-description {
  @apply mt-1 text-sm text-gray-500;
}

.user-dashboard-empty-actions {
  @apply mt-6;
}

/* ===== QUICK ACTION COMPONENTS ===== */

.user-dashboard-quick-actions {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-3;
}

.user-dashboard-quick-action {
  @apply user-dashboard-card p-4 hover:shadow-md transition-shadow duration-200 cursor-pointer;
}

.user-dashboard-quick-action-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4;
}

.user-dashboard-quick-action-title {
  @apply text-lg font-semibold text-gray-900 mb-2 text-center;
}

.user-dashboard-quick-action-description {
  @apply text-sm text-gray-600 text-center;
}

/* ===== MOBILE RESPONSIVE IMPROVEMENTS ===== */

@media (max-width: 768px) {
  .user-dashboard-container {
    @apply space-y-3;
  }

  .user-dashboard-header-title {
    @apply text-xl;
  }

  .user-dashboard-card-header {
    @apply px-3 py-4;
  }

  .user-dashboard-card-content {
    @apply px-3 py-4;
  }

  .user-dashboard-btn {
    @apply w-full justify-center;
  }

  .user-dashboard-stat-grid {
    @apply grid-cols-1 gap-3;
  }

  .user-dashboard-stat-card {
    @apply p-3;
  }

  .user-dashboard-stat-value {
    @apply text-xl;
  }

  .user-dashboard-table-header-cell,
  .user-dashboard-table-cell {
    @apply px-3 py-2;
  }

  .user-dashboard-list-item {
    @apply p-3;
  }

  .user-dashboard-list-item-content {
    @apply flex-col items-start gap-3;
  }

  .user-dashboard-list-item-actions {
    @apply w-full;
  }

  .user-dashboard-search-form {
    @apply grid-cols-1 gap-3;
  }

  .user-dashboard-quick-actions {
    @apply grid-cols-1 gap-3;
  }

  .user-dashboard-quick-action {
    @apply p-3;
  }

  .user-dashboard-quick-action-icon {
    @apply w-10 h-10 mb-3;
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

@media (max-width: 768px) {
  .user-dashboard-btn,
  .user-dashboard-form-input,
  .user-dashboard-form-select {
    @apply min-h-[44px];
  }
}

/* ===== FOCUS STATES ===== */

.user-dashboard-btn:focus,
.user-dashboard-form-input:focus,
.user-dashboard-form-select:focus {
  @apply outline-none ring-2 ring-green-500 ring-offset-2;
}
