<?php $__env->startSection('title', 'Dashboard Supplier - Indah Berkah Abadi'); ?>
<?php $__env->startSection('page-title', 'Dashboard Supplier'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Welcome Header -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Selamat Datang, <?php echo e(auth()->user()->name); ?></h1>
                    <p class="text-gray-600 mt-1">Ke<PERSON><PERSON> pengiriman dan retur produk ke Indah Berkah Abadi</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="<?php echo e(route('supplier.deliveries.create')); ?>" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Tambah Pengiriman
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Time Period Filter -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <h2 class="text-lg font-semibold text-gray-900">Filter Periode</h2>
                <form method="GET" class="flex gap-2">
                    <input type="month"
                           name="month"
                           value="<?php echo e($filterMonth); ?>"
                           class="supplier-dashboard-input" style="max-width: 200px;">
                    <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-primary supplier-dashboard-btn-sm">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Filter
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 lg:gap-6">
        <!-- Pending Deliveries -->
        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon yellow">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value"><?php echo e(number_format($deliveryStats['pending_deliveries'])); ?></div>
            <div class="supplier-dashboard-stat-label">Pengiriman Pending</div>
        </div>

        <!-- Total Returns -->
        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon red">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value"><?php echo e(number_format($returnStats['total_returns'])); ?></div>
            <div class="supplier-dashboard-stat-label">Total Retur</div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Deliveries -->
        <div class="supplier-dashboard-card">
            <div class="supplier-dashboard-card-header">
                <h2 class="supplier-dashboard-card-title">Pengiriman Terbaru</h2>
                <a href="<?php echo e(route('supplier.deliveries.index')); ?>" class="supplier-dashboard-btn supplier-dashboard-btn-secondary supplier-dashboard-btn-sm">
                    Lihat Semua
                </a>
            </div>
            <div class="supplier-dashboard-card-content">
                <?php $__empty_1 = true; $__currentLoopData = $recentDeliveries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $delivery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                    <div class="flex-1">
                        <div class="font-medium text-gray-900"><?php echo e($delivery->product->name); ?></div>
                        <div class="text-sm text-gray-500">
                            <?php echo e($delivery->supplier->name); ?> • <?php echo e(number_format($delivery->quantity)); ?> unit
                        </div>
                        <div class="text-xs text-gray-400">
                            <?php echo e(auth()->user()->formatDate($delivery->delivery_date)); ?>

                        </div>
                    </div>
                    <div class="ml-4">
                        <?php if($delivery->status === 'pending'): ?>
                            <span class="supplier-dashboard-badge supplier-dashboard-badge-warning">Pending</span>
                        <?php elseif($delivery->status === 'received'): ?>
                            <span class="supplier-dashboard-badge supplier-dashboard-badge-success">Diterima</span>
                        <?php elseif($delivery->status === 'partial'): ?>
                            <span class="supplier-dashboard-badge supplier-dashboard-badge-info">Sebagian</span>
                        <?php else: ?>
                            <span class="supplier-dashboard-badge supplier-dashboard-badge-error"><?php echo e(ucfirst($delivery->status)); ?></span>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="supplier-dashboard-empty-state">
                    <svg class="supplier-dashboard-empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    <h3 class="supplier-dashboard-empty-title">Belum Ada Pengiriman</h3>
                    <p class="supplier-dashboard-empty-description">Belum ada pengiriman pada periode ini</p>
                    <a href="<?php echo e(route('supplier.deliveries.create')); ?>" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                        Tambah Pengiriman Pertama
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Returns -->
        <div class="supplier-dashboard-card">
            <div class="supplier-dashboard-card-header">
                <h2 class="supplier-dashboard-card-title">Retur Terbaru</h2>
                <a href="<?php echo e(route('supplier.returns.index')); ?>" class="supplier-dashboard-btn supplier-dashboard-btn-secondary supplier-dashboard-btn-sm">
                    Lihat Semua
                </a>
            </div>
            <div class="supplier-dashboard-card-content">
                <?php $__empty_1 = true; $__currentLoopData = $recentReturns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $return): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                    <div class="flex-1">
                        <div class="font-medium text-gray-900"><?php echo e($return->product->name); ?></div>
                        <div class="text-sm text-gray-500">
                            <?php echo e($return->source); ?> • <?php echo e(number_format($return->quantity)); ?> unit
                        </div>
                        <div class="text-xs text-gray-400">
                            <?php echo e($return->reason_in_indonesian); ?> • <?php echo e(auth()->user()->formatDate($return->return_date)); ?>

                        </div>
                    </div>
                    <div class="ml-4">
                        <?php if($return->status === 'requested'): ?>
                            <span class="supplier-dashboard-badge supplier-dashboard-badge-warning"><?php echo e($return->status_in_indonesian); ?></span>
                        <?php elseif($return->status === 'approved'): ?>
                            <span class="supplier-dashboard-badge supplier-dashboard-badge-info"><?php echo e($return->status_in_indonesian); ?></span>
                        <?php elseif($return->status === 'completed'): ?>
                            <span class="supplier-dashboard-badge supplier-dashboard-badge-success"><?php echo e($return->status_in_indonesian); ?></span>
                        <?php elseif($return->status === 'rejected'): ?>
                            <span class="supplier-dashboard-badge supplier-dashboard-badge-error"><?php echo e($return->status_in_indonesian); ?></span>
                        <?php else: ?>
                            <span class="supplier-dashboard-badge supplier-dashboard-badge-info"><?php echo e($return->status_in_indonesian); ?></span>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="supplier-dashboard-empty-state">
                    <svg class="supplier-dashboard-empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                    </svg>
                    <h3 class="supplier-dashboard-empty-title">Belum Ada Retur</h3>
                    <p class="supplier-dashboard-empty-description">Belum ada retur pada periode ini</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.supplier', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/supplier/dashboard.blade.php ENDPATH**/ ?>